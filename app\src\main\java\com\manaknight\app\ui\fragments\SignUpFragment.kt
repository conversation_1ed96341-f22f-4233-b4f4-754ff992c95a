
package com.manaknight.app.ui.fragments

import android.os.Bundle
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import android.widget.Toast
import Manaknight.R
import Manaknight.databinding.FragmentSignUpBinding
import android.app.Dialog
import android.view.MotionEvent
import android.widget.EditText
import com.manaknight.app.extensions.checkIsEmpty
import com.manaknight.app.extensions.disableSpaces
import com.manaknight.app.extensions.hide
import com.manaknight.app.extensions.hideSoftKeyboard
import com.manaknight.app.extensions.setOnClickWithDebounce
import com.manaknight.app.extensions.show
import com.manaknight.app.extensions.snackBar
import com.manaknight.app.extensions.textToString
import com.manaknight.app.extensions.viewBinding
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import org.koin.androidx.viewmodel.ext.android.viewModel
import androidx.appcompat.app.AppCompatActivity
import com.manaknight.app.data.local.AppPreferences
import com.manaknight.app.extensions.isEmailValid
import com.manaknight.app.extensions.showProgressBar
import com.manaknight.app.model.remote.profitPro.CompanyRequest
import com.manaknight.app.utils.ProgressDialog.Companion.progressDialog
import org.koin.android.ext.android.inject

class SignUpFragment : Fragment(R.layout.fragment_sign_up) {

    private val binding by viewBinding(FragmentSignUpBinding::bind)
    private val baasViewModel: BaasViewModel by viewModel()
    private var firstName = ""
    private var lastName = ""
    private var companName = ""
    private var email = ""
    private var password = ""
    var isScrolling = false
    private lateinit var dialog: Dialog
    private val pref by inject<AppPreferences>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog = progressDialog(requireContext())
        binding.headerInclude.backButton.setOnClickListener {
            findNavController().popBackStack()
        }

        binding.btnSignin.setOnClickListener {
            findNavController().popBackStack()
        }


        binding.headerInclude.addPlaterTitle.text = "Register"

        binding.btnSignUp.setOnClickWithDebounce {
            validate()
        }

        binding.edTxtFirstName.doAfterTextChanged { firstName = it.toString() }
        binding.edTxtLastName.doAfterTextChanged { lastName = it.toString() }
        binding.edTxtCompanyName.doAfterTextChanged { companName = it.toString() }
        binding.edTxtUserName.doAfterTextChanged { email = it.toString() }
        binding.edTxtPassword.doAfterTextChanged { password = it.toString() }

        binding.edTxtFirstName.disableSpaces()
        binding.edTxtLastName.disableSpaces()
        //binding.edTxtCompanyName.disableSpaces()
        binding.edTxtUserName.disableSpaces()
        binding.edTxtPassword.disableSpaces()

        binding.line1.setOnClickListener() {
            if (!isScrolling) {
                requireActivity().hideSoftKeyboard()
            }
        }

        binding.scrollableContent.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_MOVE -> {
                    // If scrolling, mark it as scrolling
                    isScrolling = true
                    return@setOnTouchListener false  // Allow scroll but prevent other actions
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // Scrolling ended, reset the flag
                    isScrolling = false
                }
            }
            false
        }

    }

    private fun validate() {
        with(binding) {

            if(firstName.isEmpty()
                || lastName.isEmpty()
                || companName.isEmpty()
                || email.isEmpty()
                || password.checkIsEmpty()) {
                Toast.makeText(context, "Please fill in all required fields to complete your registration.", Toast.LENGTH_SHORT).show();
            } /*else if(lastName.isEmpty()) {
                Toast.makeText(context, "Please enter last name", Toast.LENGTH_SHORT).show();
            } else if(companName.isEmpty()) {
                Toast.makeText(context, "Please enter company name", Toast.LENGTH_SHORT).show();
            } else if(email.isEmpty()) {
                Toast.makeText(context, "Please enter email address", Toast.LENGTH_SHORT).show();
            }*/ else if(password.length < 8) {
                Toast.makeText(context, "Your password is too weak. Please choose a stronger password with at least 8 characters, including a number and a special character.", Toast.LENGTH_LONG).show();
            } else {
                if (binding.edTxtUserName.isEmailValid()) {
                    requireActivity().hideSoftKeyboard()
                    register()
                } else {
                    snackBar("Please enter a valid email address.")
                }

            }
        }
    }

    private fun register() {
        requireActivity().hideSoftKeyboard()
       
        baasViewModel.registerLambda(email, "company", true, true, password,firstName, lastName, "", "")
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: getString(R.string.something_went_wrong))
                        dialog.hide()
                    }
                    Status.LOADING -> {
                        //snackBar("Please wait...")
                        dialog.show()
                    }

                    Status.SUCCESS -> {
                        //findNavController().popBackStack()

                        val data = it.data

                        pref.saveUser(
                            data?.user_id?.toInt(),
                            data?.token,
                            data?.role,
                            email,
                            "",
                            firstName,
                            lastName,
                            password,
                            companName
                        )

                        companySetup()
                        //findNavController().navigate(R.id.action_signUpFragment_to_subscriptionFragment)
                    }
                }
            }
    }

    private fun companySetup() {

        baasViewModel.signupCompanySeup(CompanyRequest(companName))
            .observe(viewLifecycleOwner) {
                when (it.status) {
                    Status.ERROR -> {
                        snackBar(it.message ?: getString(R.string.something_went_wrong))
                        dialog.hide()
                    }
                    Status.LOADING -> {
                    }

                    Status.SUCCESS -> {
                        dialog.hide()
                        findNavController().navigate(R.id.action_signUpFragment_to_subscriptionFragment)
                    }
                }
            }
    }


  override fun onResume() {
            super.onResume()
            (activity as AppCompatActivity?)?.supportActionBar?.hide()
      binding.headerInclude.backButton.show()
        }

        override fun onStop() {
            super.onStop()
            (activity as AppCompatActivity?)?.supportActionBar?.show()
        }

}
    
