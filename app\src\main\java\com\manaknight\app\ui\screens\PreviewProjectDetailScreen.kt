package com.manaknight.app.ui.screens

import Manaknight.R
import android.app.Dialog
import android.util.Log
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.manaknight.app.network.Resource
import com.manaknight.app.network.Status
import com.manaknight.app.viewmodels.BaasViewModel
import androidx.lifecycle.viewmodel.compose.viewModel

import androidx.compose.material.*
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.google.android.gms.common.api.Api
import com.manaknight.app.model.remote.profitPro.AllLineItemsResponseModel
import com.manaknight.app.model.remote.profitPro.ClientDetailRespModel
import com.manaknight.app.model.remote.profitPro.DrawsRespModel
import com.manaknight.app.model.remote.profitPro.JobDetailsRespModel
import com.manaknight.app.model.remote.profitPro.MaterialRespModel
import com.manaknight.app.model.remote.profitPro.TotalRespModel


import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.runtime.*
import androidx.compose.ui.*
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.*
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.manaknight.app.utils.CustomUtils.clearCachedProjectDetails
import com.manaknight.app.utils.CustomUtils.getCachedProjectDetailsFromLocal
import java.time.LocalDate
import java.time.format.DateTimeFormatter

// Add imports for comparison logic
enum class ChangeStatus {
    NO_CHANGE,
    ADDED,
    EDITED_UP,
    EDITED_DOWN,
    DELETED
}

data class JobDetailsUIModel(
    val original: JobDetailsRespModel,
    val previous: JobDetailsRespModel? = null,
    val changeStatus: ChangeStatus
)
import com.manaknight.app.ui.components.CustomCheckbox
import com.manaknight.app.ui.utils.isTabletLayout
import com.manaknight.app.ui.utils.getMaxContentWidth


data class Material(
    val id: Int,
    val name: String,
    val cost: Double,
    val quantity: Int,
    val total: Double
)


var dontShow: Boolean =false


@Composable
fun ProjectDetailMainScreen(
    projectId: Int,
    baasViewModel: BaasViewModel,
    onClick: (String) -> Unit,
    navController: NavController,
    dialog: Dialog,
    onNavigateBack: () -> Unit
) {
//    val projectDetailsResource by baasViewModel.getSingleProjectDetails(projectId).observeAsState()

//    LaunchedEffect(Unit) {
//        baasViewModel.getSingleProjectDetailsModified(projectId)
//    }
    val context = LocalContext.current

    val cachedData = remember {
        mutableStateOf<AllLineItemsResponseModel?>(null)
    }

    LaunchedEffect(Unit) {
        cachedData.value = getCachedProjectDetailsFromLocal(context)
    }

    val cachedProjectDetailsResource = cachedData.value
//    val cachedProjectDetailsResource by baasViewModel.cachedProjectDetailsResource.observeAsState()

    Log.d("TAG", "Row88:  ${cachedProjectDetailsResource}")

    LaunchedEffect(projectId) {
        baasViewModel.setProjectId(projectId)
    }
    val projectDetailsResource by baasViewModel.projectDetailsResource.observeAsState()
    when (projectDetailsResource?.status) {
        Status.LOADING -> dialog.show()
        Status.ERROR -> {
            Text(text = "Error: ${projectDetailsResource?.message}", color = Color.Red)
            dialog.hide()
        }
        Status.SUCCESS -> {
            dialog.hide()
            projectDetailsResource?.data?.let { apiResponse ->
                ProjectDetailContent(apiResponse, onClick,navController, cachedProjectDetailsResource, baasViewModel, onNavigateBack)
            } ?: Text("No data available")
        }
        else -> Text(text = "No Data Available")
    }

}


@Composable
fun ProjectDetailContent(
    apiResponse: AllLineItemsResponseModel,
    onClick: (String) -> Unit,
    navController: NavController,
    cachedProjectDetailsResource: AllLineItemsResponseModel?,
    baasViewModel: BaasViewModel,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val dateToDisplay = apiResponse.create_at ?: LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))
    val isTablet = isTabletLayout()
    val maxContentWidth = getMaxContentWidth()

    Surface(color = Color.White) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            LazyColumn(
                modifier = Modifier
                    .then(
                        if (isTablet && maxContentWidth != androidx.compose.ui.unit.Dp.Unspecified)
                            Modifier
                                .align(Alignment.TopCenter)
                                .widthIn(max = maxContentWidth)
                        else Modifier.fillMaxSize()
                    )
                    .padding(16.dp)
                    .background(Color.White),
            ) {
                item {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        IconButton(onClick = {
                            navController.popBackStack()
                        }) {
                            Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                        }
                        Text(
                            text = apiResponse.client_details.name ?: "name" ?: "Company Name",
                            fontWeight = FontWeight.Bold,
                            color = colorResource(R.color.profit_black),
                            fontSize = 16.sp
                        )
                        IconButton(onClick = { /* Handle more options */ }) {

                            Icon(
                                painter = painterResource(id = R.drawable.ic_options_bold),
                                contentDescription = "More",
                                tint = colorResource(com.saksham.customloadingdialog.R.color.transparent),
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }

                }


                item {

                    Text(
                        text = "${dateToDisplay}",
                        fontSize = 14.sp,
                        modifier = Modifier.padding(bottom = 7.dp)
                    )
                    Divider(color = Color.Gray.copy(alpha = 0.3f))
                }

                item {
                    Spacer(modifier = Modifier.height(7.dp))
                    Text(
                        text = "Client Details",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,

                        )
                    Spacer(modifier = Modifier.height(4.dp))
                    apiResponse.client_details?.let { ClientDetailsCard(it) }
                }

                item {
                    Text(
                        text = "Line Items",
                        fontSize = 16.sp,
                        modifier = Modifier.padding(bottom = 1.dp, top = 16.dp),
                        fontWeight = FontWeight.Medium, color = colorResource(R.color.profit_black)
                    )
                }

                val currentJobs = apiResponse.job_details ?: emptyList()
                val previousJobs = cachedProjectDetailsResource?.job_details ?: emptyList()

                // Use compareLineItems function for comprehensive comparison
                val lineItems = remember(currentJobs, previousJobs) {
                    compareLineItems(currentJobs, previousJobs)
                }

                Log.d("PreviewProjectScreen", "=== PREVIEW PROJECT DATA COMPARISON ===")
                Log.d("PreviewProjectScreen", "Current jobs count: ${currentJobs.size}")
                Log.d("PreviewProjectScreen", "Cached jobs count: ${previousJobs.size}")
                Log.d("PreviewProjectScreen", "Comparison result: ${lineItems.size} items")
                lineItems.forEachIndexed { index, item ->
                    Log.d("PreviewProjectScreen", "Result $index: ID=${item.original.line_id}, Status=${item.changeStatus}")
                }
                Log.d("PreviewProjectScreen", "=== END PREVIEW PROJECT COMPARISON ===")

                itemsIndexed(lineItems) { index, uiModel ->
                    JobDetailCard(
                        jobDetail = uiModel.original,
                        previousJobDetail = uiModel.previous,
                        index = index,
                        changeStatus = uiModel.changeStatus
                    )
                }

//            apiResponse.job_details?.let { jobDetails ->
//
//                itemsIndexed(jobDetails) { index, job ->
//                    JobDetailCard(jobDetail = job, index = index)
//                }
//            }


                item {
                    Text(
                        text = "Project Totals",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.profit_black),
                        modifier = Modifier.padding(bottom = 1.dp, top = 16.dp)
                    )
                    Spacer(modifier = Modifier.height(1.dp))
                    apiResponse.totals?.let { ProjectTotalsCard(it) }


//                Spacer(modifier = Modifier.height(1.dp))
//                Card(
//                    shape = RoundedCornerShape(12.dp),
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .padding(vertical = 8.dp).border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(12.dp))
//                ) {
//                    Column(
//                        modifier = Modifier
//                            .background(Color.White)
//                            .padding(16.dp)
//                    ) {
//                        DetailRow(
//                            label = "Total cost",
//                            value = "$${
//                                String.format(
//                                    "%.2f",
//                                    apiResponse.totals.sale_price?.toDouble()
//                                )
//                            }"
//                        ) // Adjust as necessary
//                    }
//                }
                }


                item {
                    Text(
                        text = "Stages of Payment",
                        fontSize = 16.sp,
                        modifier = Modifier.padding(bottom = 1.dp, top = 16.dp),
                        color = colorResource(R.color.profit_black),
                        fontWeight = FontWeight.Medium,
                    )
                    Spacer(modifier = Modifier.height(1.dp))
                    apiResponse.draws?.let { PaymentStagesCard(it) }


                }

                item {

                    Spacer(modifier = Modifier.height(1.dp))
                    Card(
                        shape = RoundedCornerShape(12.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp).border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(12.dp))
                    ) {
                        Column(
                            modifier = Modifier
                                .background(Color.White)
                                .padding(16.dp)
                        ) {
                            DetailRow(
                                label = "Total cost",
                                value = "$${
                                    String.format(
                                        "%.2f",
                                        apiResponse.totals.sale_price?.toDouble()
                                    )
                                }"
                            ) // Adjust as necessary
                        }
                    }
                }




                item {
                    if (!dontShow) {
                        CheckboxWithLabel(dontShow = dontShow)
                    }

                    val projectStatus =apiResponse.project.status

                    SendEstimateButton(onClick = {
                        apiResponse.client_details.email?.let { onClick(it) }

                        baasViewModel.cacheProjectDetailsResource(null)
                        clearCachedProjectDetails(context)

                    },
                        projectStatus)
                    EditButton(onClick = { onNavigateBack() })
                }
            }

        }
    }
}



@Composable
fun CheckboxWithLabel(dontShow: Boolean) {
    Spacer(modifier = Modifier.height(1.dp))
    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(12.dp))
    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .padding(16.dp)
        ) {
            Text(
                text = "Clicking the 'Send invoice' button below will save this as a project and send an invoice to your customer. The invoice will not include any of your budgets or profit overhead information. Only sale price and item details will be shown",
                fontSize = 14.sp,
                lineHeight = 20.sp,
                modifier = Modifier.padding(bottom = 1.dp, top = 16.dp),
                color = colorResource(R.color.profit_black),
                fontWeight = FontWeight.Medium,
            )

            Row(verticalAlignment = Alignment.CenterVertically) {
                var checked by remember { mutableStateOf(dontShow) }
                CustomCheckbox(checked = checked, onCheckedChange = { checked = it })
                Text(
                    text = "Don't show again",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }




        }
    }


}

@Composable
fun SendEstimateButton(onClick: () -> Unit, status: Int) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()

            .padding(vertical = 8.dp).height(44.dp),
        colors = ButtonDefaults.buttonColors( colorResource(R.color.brand_green)),
        shape = RoundedCornerShape(8.dp) // reduced radius
    ) {
        val message = if (status == 1 || status == 4) "Send Invoice to the Client" else "Send Estimate to the Client"

        Text(
            text = message,
            color = Color.Black
        )

    }
}

@Composable
fun EditButton(onClick: () -> Unit) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp).height(44.dp).border(1.dp, colorResource(R.color.profit_black), RoundedCornerShape(8.dp)),
        colors = ButtonDefaults.buttonColors( Color.White),
        shape = RoundedCornerShape(8.dp) // reduced radius
    ) {
        Text(text = "Edit", color = Color.Black)
    }
}

@Composable
fun JobDetailCard(
    jobDetail: JobDetailsRespModel,
    previousJobDetail: JobDetailsRespModel?,
    index: Int,
    changeStatus: ChangeStatus = ChangeStatus.NO_CHANGE
) {
    var isExpanded by remember { mutableStateOf(false) }

    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(12.dp))
    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    jobDetail?.description?.let {
                        Text(
                            text = "${index + 1}.  ",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.profit_black)
                        )
                        Text(
                            text = it,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.profit_black)
                        )
                    }

                    // Add change status indicator
                    when (changeStatus) {
                        ChangeStatus.ADDED -> {
                            Spacer(modifier = Modifier.width(8.dp))
                            Box(
                                modifier = Modifier
                                    .background(Color(0xFF4CAF50), RoundedCornerShape(4.dp))
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = "NEW",
                                    fontSize = 10.sp,
                                    color = Color.White,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                        ChangeStatus.EDITED_UP -> {
                            Spacer(modifier = Modifier.width(8.dp))
                            Box(
                                modifier = Modifier
                                    .background(Color(0xFF4CAF50), RoundedCornerShape(4.dp))
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = "INCREASED",
                                    fontSize = 10.sp,
                                    color = Color.White,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                        ChangeStatus.EDITED_DOWN -> {
                            Spacer(modifier = Modifier.width(8.dp))
                            Box(
                                modifier = Modifier
                                    .background(Color(0xFFF44336), RoundedCornerShape(4.dp))
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = "CHANGED",
                                    fontSize = 10.sp,
                                    color = Color.White,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                        ChangeStatus.DELETED -> {
                            Spacer(modifier = Modifier.width(8.dp))
                            Box(
                                modifier = Modifier
                                    .background(Color(0xFFF44336), RoundedCornerShape(4.dp))
                                    .padding(horizontal = 6.dp, vertical = 2.dp)
                            ) {
                                Text(
                                    text = "DELETED",
                                    fontSize = 10.sp,
                                    color = Color.White,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                        }
                        ChangeStatus.NO_CHANGE -> {
                            // No indicator for unchanged items
                        }
                    }
                }
                IconButton(onClick = { isExpanded = !isExpanded }) {
                    Icon(
                        painter = painterResource(id = if (isExpanded) R.drawable.chevron_up else R.drawable.chevron_bottom),
                        contentDescription = "Expand/Collapse",

                        modifier = Modifier.size(24.dp),
                        tint = colorResource(R.color.text_sub),
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))
            Row {
                Text(
                    text = "Estimated by ",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = colorResource(id = R.color.profit_black)
                )
                Box(
                    modifier = Modifier
                        .background(colorResource(id = R.color.project_blue_bg), shape = RoundedCornerShape(4.dp))
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    val formattedEstimate = jobDetail?.estimated_by
                        ?.replace("_", " ")         // Replace underscores with spaces
                        ?.split(" ")                // Split into words
                        ?.joinToString(" ") { it.capitalize() } // Capitalize each word
                        ?: ""

                    Text(
                        text = formattedEstimate,
                        color = colorResource(id = R.color.project_blue),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }

            }



            if (isExpanded) {
                Spacer(modifier = Modifier.height(8.dp))

                jobDetail.labour_hours?.let { current ->
                    val previous = previousJobDetail?.labour_hours
                    val hasChanged = previous?.toString() != current.toString()

                    DetailRow(
                        label = "Labor Hours",
                        value = "$current",
                        previousValue = previous?.toString()?.let { "$it" },
                        hasChanged = hasChanged
                    )
                }
                Spacer(modifier = Modifier.height(8.dp))

                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    jobDetail.materials.forEach { material ->
                        MaterialItem(material = material, previousJobDetail?.materials)
                    }
                }






                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
//                    jobDetail.labour_hours?.let { current ->
//                        val previous = previousJobDetail?.labour_hours
//                        val hasChanged = previous?.toString() != current.toString()
//
//                        DetailRow(
//                            label = "Labor Hours",
//                            value = "$current",
//                            previousValue = previous?.toString()?.let { "$it" },
//                            hasChanged = hasChanged
//                        )
//                    }

                    jobDetail.material_budget?.let { current ->
                        val previous = previousJobDetail?.material_budget
                        val hasChanged = previous?.toString() != current.toString()

                        DetailRow(
                            label = "Material Budget",
                            value = "$${String.format("%.2f", current.toDouble())}",
                            previousValue = previous?.toDouble()?.let { "$${String.format("%.2f", it)}" },
                            hasChanged = hasChanged
                        )
                    }

                    jobDetail.profit_overhead_amount?.let { current ->
                        val previous = previousJobDetail?.profit_overhead_amount
                        val hasChanged = previous?.toString() != current.toString()

                        DetailRow(
                            label = "Profit Overhead",
                            value = "$${String.format("%.2f", current.toDouble())}",
                            previousValue = previous?.toDouble()?.let { "$${String.format("%.2f", it)}" },
                            hasChanged = hasChanged
                        )
                    }

                    jobDetail.sale_price?.let { current ->
                        val previous = previousJobDetail?.sale_price
                        val hasChanged = previous?.toString() != current.toString()

                        DetailRow(
                            label = "Sale Price",
                            value = "$${String.format("%.2f", current.toDouble())}",
                            previousValue = previous?.toDouble()?.let { "$${String.format("%.2f", it)}" },
                            hasChanged = hasChanged
                        )
                    }


                    Spacer(modifier = Modifier.height(16.dp))

//                    jobDetail.materials.forEach { material ->
//                        MaterialDetailRow(material)
//                    }
                }
            }
        }
    }
}

@Composable
fun MaterialDetailRow(material: MaterialRespModel) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        material.name?.let { Text(text = it, fontSize = 14.sp, color = Color.DarkGray) }
        Text(text = "$${material.total}", fontSize = 14.sp, fontWeight = FontWeight.Bold, color = Color.Black)
    }
}

@OptIn(ExperimentalMaterial3Api::class) // Needed for certain Material 3 components
@Composable
fun MaterialItem(
    material: MaterialRespModel,
    previousMaterials: List<MaterialRespModel>?
) {
    // Find matching material by ID
    val previousMaterial = previousMaterials?.find { it.id == material.id }

    // Get current and previous totals
    val currentTotal = material.total
    val previousTotal = previousMaterial?.total
    Log.d("TAG", "Row2: ${previousTotal}   ${currentTotal}  ${previousMaterial} ${previousMaterial}")
    val hasChanged = previousTotal != null && currentTotal != previousTotal
    val changeAmount = if (hasChanged) {
        if(previousTotal != null && currentTotal != null){
//            currentTotal - previousTotal
            currentTotal.toDouble() - previousTotal.toDouble()
        }
        else{
            0.0
        }
    } else 0.0

//    val changeAmount = currentTotal - previousTotal
//    val changeAmount = (material.total ?: 0) - (previousMaterial?.total ?: 0)



    val textColor = when {
        changeAmount > 0 -> Color(0xFF4CAF50) // Green
        changeAmount < 0 -> Color(0xFFF44336) // Red
        else -> Color.Black
    }

    Column(
        modifier = Modifier
            .background(colorResource(R.color.light_gray_bg), shape = RoundedCornerShape(8.dp)) // light gray background
            .padding(16.dp)
            .fillMaxWidth()
    ) {
        Text(
            text = material.name ?: "",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(4.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "$1/unit",
                style = MaterialTheme.typography.bodySmall,
                color = textColor
            )

            Text(
                text = "${material.quantity} units",
                style = MaterialTheme.typography.bodySmall,
                color = textColor
            )

            Text(
                text = "Total: $${material.total}",
                style = MaterialTheme.typography.bodySmall,
                color = textColor
            )
        }
    }
}



@Composable
fun DetailRow(
    label: String?,
    value: String?,
    previousValue: String? = null,
    hasChanged: Boolean = false
) {

    val new = value?.replace(Regex("[^\\d.]"), "")?.toDoubleOrNull()
    val old = previousValue?.replace(Regex("[^\\d.]"), "")?.toDoubleOrNull()
    Log.d("TAG", "Row1: ${old}   ${new} ${previousValue}")
    val hasChanged = old != null && new != old
    val changeAmount = if (hasChanged) {
        if (new != null && old != null){
            new.toDouble() - old.toDouble()
        }
        else{
            0.0
        }
    } else 0.0

    val changeColor = when {
        changeAmount > 0 -> Color(0xFF4CAF50) // green
        changeAmount < 0 -> Color(0xFFF44336) // red
        else -> Color.Black
    }

    val difference = if (new != null && old != null) new - old else null

    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            label?.let {
                Text(
                    text = it,
                    fontSize = 14.sp,
                    color = colorResource(id = R.color.text_sub)
                )
            }

            if (hasChanged && old != null && difference != null) {
                val sign = if (difference > 0) "+" else ""
                Row(


                ) {
                    value?.let {
                        Text(
                            text = it,
                            fontSize = 14.sp,
                            color = colorResource(id = R.color.profit_black),
                            fontWeight = FontWeight.Medium
                        )
                    }
                    Text(
                        text = " (${sign}${String.format("%.2f", difference)})",
                        fontSize = 14.sp,
                        color = changeColor,
                        modifier = Modifier.padding(start = 4.dp, top = 2.dp)
                    )
                }
            }
            else{
                value?.let {
                    Text(
                        text = it,
                        fontSize = 14.sp,
                        color = colorResource(id = R.color.profit_black),
                        fontWeight = FontWeight.Medium
                    )
                }
            }

        }


    }
}



@Composable
fun ProjectTotalsCard(totals: TotalRespModel) {
    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(12.dp))


    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)

        ) {

            DetailRow(label = "Total Sale Price", value = "$${String.format("%.2f", totals.sale_price?.toDouble())}")
            DetailRow(label = "Total Profit Overhead", value = "$${String.format("%.2f", totals.total_profit_overhead?.toDouble())}")
            DetailRow(label = "Total Material Budget", value = "$${String.format("%.2f", totals.material_budget?.toDouble())}")

            DetailRow(label = "Total Labour Budget", value = "$${String.format("%.2f", totals.labour_budget?.toDouble())}")




        }
    }
}

@Composable
fun PaymentStagesCard(details: List<DrawsRespModel>) {
    //                        job details
    Card(modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        border = BorderStroke(1.dp, colorResource(R.color.stroke_soft)),

        colors = CardDefaults.cardColors(Color.White)) {
        Column() {

            details.forEachIndexed  { index,job ->
                Column(
                    modifier = Modifier.fillMaxWidth().padding(16.dp), verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row {
                        // Display index
                        Text(
                            text = "${index + 1}. ",
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.profit_black),
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        Spacer(modifier = Modifier.height(2.dp))
                        Text("${job.percentage}% " ?: "", fontWeight = FontWeight.Medium,color = colorResource(R.color.profit_black))
                        Text("($${job.amount})" ?: "", fontWeight = FontWeight.Medium,color = colorResource(R.color.profit_black))

                    }
                    Spacer(modifier = Modifier.height(2.dp))

                    Text(job.description ?: "description",fontSize = 14.sp, color = colorResource(R.color.profit_black))
                    // Only show divider if not the last item
                    if (index != details.lastIndex) {
                        Divider(color = Color.Gray.copy(alpha = 0.3f))
                    }
                }


            }
        }
    }
}

@Composable
fun ClientDetailsCard(clientDetails: ClientDetailRespModel) {
    Card(
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp).border(1.dp, colorResource(R.color.stroke_soft), RoundedCornerShape(12.dp))
    ) {
        Column(
            modifier = Modifier
                .background(Color.White)
                .padding(16.dp)
        ) {

            Column(modifier = Modifier.background(Color.White).fillMaxWidth(), verticalArrangement = Arrangement.spacedBy(8.dp)) {

                Text(text = clientDetails.name ?: "N/A", fontSize = 14.sp, fontWeight = FontWeight.Medium, color = colorResource(R.color.profit_black))
                Text(text = clientDetails.email ?: "N/A", fontSize = 14.sp,color = colorResource(R.color.profit_black))
                Text(text = clientDetails.address ?: "N/A", fontSize = 14.sp,color = colorResource(R.color.profit_black))
            }

        }
    }
}



@Preview(showBackground = true, widthDp = 800, heightDp = 1200, name = "Tablet Layout")
@Composable
fun PreviewProjectDetailScreen() {
    // TODO: Provide a mock AllLineItemsResponseModel and other params if needed for preview
    // ProjectDetailContent(apiResponse = mockApiResponse, onClick = {}, navController = rememberNavController(), cachedProjectDetailsResource = null, baasViewModel = BaasViewModel(), onNavigateBack = {})
}

// Comprehensive comparison function for line items
fun compareLineItems(
    newItems: List<JobDetailsRespModel>,
    oldItems: List<JobDetailsRespModel>?
): List<JobDetailsUIModel> {
    if (oldItems == null) {
        return newItems.map { JobDetailsUIModel(it, null, ChangeStatus.NO_CHANGE) }
    }

    val result = mutableListOf<JobDetailsUIModel>()

    // Check for new and modified items
    newItems.forEach { newItem ->
        val oldItem = oldItems.find { it.line_id == newItem.line_id }

        if (oldItem == null) {
            result.add(JobDetailsUIModel(newItem, null, ChangeStatus.ADDED))
        } else {
            val saleChanged = newItem.sale_price != oldItem.sale_price
            val labourChanged = newItem.labour_budget != oldItem.labour_budget
            val materialChanged = newItem.material_budget != oldItem.material_budget
            val profitChanged = newItem.profit_overhead_amount != oldItem.profit_overhead_amount
            val estimatedChanged = newItem.estimated_by != oldItem.estimated_by
            val descriptionChanged = newItem.description != oldItem.description

            val status = when {
                saleChanged && newItem.sale_price != null && oldItem.sale_price != null -> {
                    if (newItem.sale_price > oldItem.sale_price) ChangeStatus.EDITED_UP
                    else ChangeStatus.EDITED_DOWN
                }
                labourChanged || materialChanged || profitChanged || estimatedChanged || descriptionChanged -> ChangeStatus.EDITED_DOWN
                else -> ChangeStatus.NO_CHANGE
            }

            result.add(JobDetailsUIModel(newItem, oldItem, status))
        }
    }

    // Check for deleted items (items that exist in old but not in new)
    oldItems.forEach { oldItem ->
        val existsInNew = newItems.any { it.line_id == oldItem.line_id }
        if (!existsInNew) {
            result.add(JobDetailsUIModel(oldItem, oldItem, ChangeStatus.DELETED))
        }
    }

    return result
}

// Preview function for your composable
//@Preview(showBackground = true)
//@Composable
//fun PreviewInvoiceScreen() {
//    InvoiceScreen()  // Preview the InvoiceScreen composable
//}