<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_line_material" modulePackage="Manaknight" filePath="app\src\main\res\layout\item_line_material.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_line_material_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="127" endOffset="35"/></Target><Target id="@+id/checkBoxMaterial" view="CheckBox"><Expressions/><location startLine="21" startOffset="8" endLine="28" endOffset="54"/></Target><Target id="@+id/txtMaterialName" view="TextView"><Expressions/><location startLine="31" startOffset="8" endLine="44" endOffset="54"/></Target><Target id="@+id/txtPrice" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="57" endOffset="70"/></Target><Target id="@+id/txtTotalPrice" view="TextView"><Expressions/><location startLine="59" startOffset="8" endLine="70" endOffset="70"/></Target><Target id="@+id/txtUnitStart" view="TextView"><Expressions/><location startLine="73" startOffset="8" endLine="84" endOffset="63"/></Target><Target id="@+id/txtUnit" view="TextView"><Expressions/><location startLine="86" startOffset="8" endLine="98" endOffset="63"/></Target><Target id="@+id/edtUnits" view="EditText"><Expressions/><location startLine="101" startOffset="8" endLine="117" endOffset="54"/></Target></Targets></Layout>